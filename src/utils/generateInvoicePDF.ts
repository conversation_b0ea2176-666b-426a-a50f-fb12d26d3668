import html2canvas from "html2canvas";
import jsPDF from "jspdf";

// Conversion factor from pixels to millimeters
// Based on 96 DPI standard: 25.4mm per inch / 96 pixels per inch = 0.2********* mm/pixel
const PIXELS_TO_MM = 25.4 / 96;

// HTML escape utility to prevent XSS in PDF template
function escapeHtml(unsafe: unknown): string {
  if (unsafe == null) return "";
  
  const str = String(unsafe);
  return str
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#39;")
    .replace(/\//g, "&#x2F;");
}

export interface InvoiceData {
  invoiceNumber?: string;
  invoiceDate?: number;
  dueDate?: number;
  fromCompany: {
    name: string;
    address: string;
    phone: string;
    email: string;
    vatNumber?: string;
  };
  toCompany: {
    name: string;
    address: string;
    phone: string;
    email: string;
    vatNumber?: string;
  };
  services: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
    details?: {
      dealType?: string;
      commissionRate?: string;
      dealValue?: string;
      partnerTier?: string;
    };
  }>;
  financials: {
    subtotal: number;
    vatRate: number;
    vatAmount: number;
    total: number;
  };
  notes?: string;
}

export async function generateInvoicePDF(
  invoiceData: InvoiceData,
  elementId?: string
): Promise<void> {
  let tempElement: HTMLElement | null = null;
  
  try {
    let element: HTMLElement | null = null;
    
    if (elementId) {
      // Use existing element if provided
      element = document.getElementById(elementId);
    } else {
      // Create a temporary element with the invoice content
      element = createInvoiceElement(invoiceData);
      tempElement = element; // Track temp element for cleanup
      document.body.appendChild(element);
    }
    
    if (!element) {
      throw new Error("Invoice element not found");
    }

    // Configure html2canvas options for better quality
    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      logging: false,
      backgroundColor: "#ffffff",
      width: element.scrollWidth,
      height: element.scrollHeight,
    });

    // Create PDF
    const imgData = canvas.toDataURL("image/png");
    
    // A4 size: 210 x 297 mm
    const pdf = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // Calculate dimensions to fit the invoice on the page
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const margin = 10;
    
    const availableWidth = pdfWidth - (margin * 2);
    const availableHeight = pdfHeight - (margin * 2);
    
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    const ratio = Math.min(availableWidth / (imgWidth * PIXELS_TO_MM), availableHeight / (imgHeight * PIXELS_TO_MM));
    
    const finalWidth = imgWidth * PIXELS_TO_MM * ratio;
    const finalHeight = imgHeight * PIXELS_TO_MM * ratio;
    
    // Center the image on the page
    const x = (pdfWidth - finalWidth) / 2;
    const y = margin;

    pdf.addImage(imgData, "PNG", x, y, finalWidth, finalHeight);

    // Generate filename
    const filename = generateFilename(invoiceData);
    
    // Download the PDF
    pdf.save(filename);
    
    // Show success message
    const event = new CustomEvent('show-toast', { 
      detail: { message: `PDF downloaded: ${filename}`, type: 'success' } 
    });
    window.dispatchEvent(event);
    
  } catch (error) {
    console.error("Error generating PDF:", error);
    const event = new CustomEvent('show-toast', { 
      detail: { message: "Failed to generate PDF", type: 'error' } 
    });
    window.dispatchEvent(event);
  } finally {
    // Ensure cleanup of temporary DOM element even if errors occur
    if (tempElement && tempElement.parentNode) {
      try {
        tempElement.parentNode.removeChild(tempElement);
      } catch (cleanupError) {
        console.warn("Failed to cleanup temporary DOM element:", cleanupError);
      }
    }
  }
}

function createInvoiceElement(invoiceData: InvoiceData): HTMLElement {
  const element = document.createElement("div");
  element.style.cssText = `
    width: 800px;
    background: white;
    color: black;
    font-family: Arial, sans-serif;
    padding: 40px;
    position: absolute;
    top: -10000px;
    left: -10000px;
  `;
  
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return "N/A";
    return new Date(timestamp).toLocaleDateString();
  };

  element.innerHTML = `
    <!-- Invoice Header -->
    <div style="background: linear-gradient(135deg, #1e40af, #1e3a8a); color: white; padding: 32px; margin: -40px -40px 32px -40px;">
      <div style="display: flex; justify-content: space-between; align-items: flex-start;">
        <div>
          <h1 style="font-size: 32px; font-weight: bold; margin: 0 0 8px 0;">INVOICE</h1>
          <p style="color: #bfdbfe; margin: 0;">#${escapeHtml(invoiceData.invoiceNumber || "DRAFT")}</p>
        </div>
        <div style="text-align: right;">
          <div style="font-size: 14px; opacity: 0.9; margin-bottom: 4px;">Invoice Date</div>
          <div style="font-weight: 600;">${escapeHtml(formatDate(invoiceData.invoiceDate))}</div>
          <div style="font-size: 14px; opacity: 0.9; margin: 12px 0 4px 0;">Due Date</div>
          <div style="font-weight: 600;">${escapeHtml(formatDate(invoiceData.dueDate))}</div>
        </div>
      </div>
    </div>

    <div style="padding: 32px 0;">
      <!-- Company Information -->
      <div style="display: flex; justify-content: space-between; margin-bottom: 32px;">
        <div style="flex: 1;">
          <h3 style="font-size: 14px; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em; margin: 0 0 8px 0;">From</h3>
          <div style="color: black;">
            <h2 style="font-size: 20px; font-weight: bold; margin: 0 0 4px 0;">${escapeHtml(invoiceData.fromCompany.name)}</h2>
            <div style="color: #374151; line-height: 1.5;">
              ${invoiceData.fromCompany.address ? `<p style="margin: 4px 0;">${escapeHtml(invoiceData.fromCompany.address)}</p>` : ""}
              ${invoiceData.fromCompany.email ? `<p style="margin: 4px 0;">${escapeHtml(invoiceData.fromCompany.email)}</p>` : ""}
              ${invoiceData.fromCompany.phone ? `<p style="margin: 4px 0;">${escapeHtml(invoiceData.fromCompany.phone)}</p>` : ""}
              ${invoiceData.fromCompany.vatNumber ? `<p style="margin: 4px 0;">VAT: ${escapeHtml(invoiceData.fromCompany.vatNumber)}</p>` : ""}
            </div>
          </div>
        </div>
        
        <div style="flex: 1;">
          <h3 style="font-size: 14px; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em; margin: 0 0 8px 0;">Bill To</h3>
          <div style="color: black;">
            <h2 style="font-size: 20px; font-weight: bold; margin: 0 0 4px 0;">${escapeHtml(invoiceData.toCompany.name)}</h2>
            <div style="color: #374151; line-height: 1.5;">
              ${invoiceData.toCompany.address ? `<p style="margin: 4px 0;">${escapeHtml(invoiceData.toCompany.address)}</p>` : ""}
              ${invoiceData.toCompany.email ? `<p style="margin: 4px 0;">${escapeHtml(invoiceData.toCompany.email)}</p>` : ""}
              ${invoiceData.toCompany.phone ? `<p style="margin: 4px 0;">${escapeHtml(invoiceData.toCompany.phone)}</p>` : ""}
              ${invoiceData.toCompany.vatNumber ? `<p style="margin: 4px 0;">VAT: ${escapeHtml(invoiceData.toCompany.vatNumber)}</p>` : ""}
            </div>
          </div>
        </div>
      </div>
      
      <!-- Services Table -->
      <div style="margin-bottom: 32px;">
        <div style="border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr style="background: #f9fafb;">
                <th style="padding: 12px 16px; text-align: left; font-size: 14px; font-weight: 500; color: #111827; border-bottom: 1px solid #e5e7eb;">Description</th>
                <th style="padding: 12px 16px; text-align: center; font-size: 14px; font-weight: 500; color: #111827; border-bottom: 1px solid #e5e7eb;">Qty</th>
                <th style="padding: 12px 16px; text-align: right; font-size: 14px; font-weight: 500; color: #111827; border-bottom: 1px solid #e5e7eb;">Rate</th>
                <th style="padding: 12px 16px; text-align: right; font-size: 14px; font-weight: 500; color: #111827; border-bottom: 1px solid #e5e7eb;">Amount</th>
              </tr>
            </thead>
            <tbody>
              ${invoiceData.services.map((service, index) => `
                <tr style="${index < invoiceData.services.length - 1 ? 'border-bottom: 1px solid #f3f4f6;' : ''}">
                  <td style="padding: 16px;">
                    <div style="font-weight: 500; color: #111827;">${escapeHtml(service.description)}</div>
                    ${service.details ? `
                      <div style="font-size: 14px; color: #6b7280; margin-top: 4px;">
                        Deal Value: ${escapeHtml(service.details.dealValue)} • Commission: ${escapeHtml(service.details.commissionRate)}
                      </div>
                    ` : ''}
                  </td>
                  <td style="padding: 16px; text-align: center; color: #111827;">${escapeHtml(service.quantity.toString())}</td>
                  <td style="padding: 16px; text-align: right; color: #111827;">$${escapeHtml(service.unitPrice.toFixed(2))}</td>
                  <td style="padding: 16px; text-align: right; font-weight: 500; color: #111827;">$${escapeHtml(service.total.toFixed(2))}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Totals -->
      <div style="display: flex; justify-content: flex-end; margin-bottom: 32px;">
        <div style="width: 320px;">
          <div style="line-height: 1.5;">
            <div style="display: flex; justify-content: space-between; padding: 8px 0;">
              <span style="color: #6b7280;">Subtotal:</span>
              <span style="color: #111827;">$${escapeHtml(invoiceData.financials.subtotal.toFixed(2))}</span>
            </div>
            ${invoiceData.financials.vatRate > 0 ? `
              <div style="display: flex; justify-content: space-between; padding: 8px 0;">
                <span style="color: #6b7280;">VAT (${escapeHtml(invoiceData.financials.vatRate.toString())}%):</span>
                <span style="color: #111827;">$${escapeHtml(invoiceData.financials.vatAmount.toFixed(2))}</span>
              </div>
            ` : ''}
            <div style="border-top: 1px solid #e5e7eb; padding-top: 8px; margin-top: 8px;">
              <div style="display: flex; justify-content: space-between; padding: 8px 0;">
                <span style="font-size: 18px; font-weight: 600; color: #111827;">Total:</span>
                <span style="font-size: 18px; font-weight: bold; color: #111827;">$${escapeHtml(invoiceData.financials.total.toFixed(2))}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Payment Information -->
      <div style="background: #eff6ff; border: 1px solid #bfdbfe; padding: 24px; border-radius: 8px; margin-bottom: 24px;">
        <h4 style="font-weight: 600; color: #111827; margin: 0 0 12px 0;">Payment Information</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; font-size: 14px;">
          <div>
            <p style="font-weight: 500; color: #374151; margin: 0 0 4px 0;">Payment Due Date:</p>
            <p style="color: #111827; margin: 0;">${escapeHtml(formatDate(invoiceData.dueDate))}</p>
          </div>
          <div>
            <p style="font-weight: 500; color: #374151; margin: 0 0 4px 0;">Payment Terms:</p>
            <p style="color: #111827; margin: 0;">Net 30 Days</p>
          </div>
        </div>
      </div>

      <!-- Bank Details -->
      <div style="background: #eff6ff; border: 1px solid #bfdbfe; padding: 24px; border-radius: 8px; margin-bottom: 24px;">
        <h4 style="font-weight: 600; color: #111827; margin: 0 0 12px 0;">Bank Transfer Details</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; font-size: 14px;">
          <div style="line-height: 1.6;">
            <p style="margin: 8px 0;"><span style="font-weight: 500; color: #374151;">Bank:</span> UNION BANK B.M.</p>
            <p style="margin: 8px 0;"><span style="font-weight: 500; color: #374151;">Branch:</span> 079 Herzliya Pituah</p>
            <p style="margin: 8px 0;"><span style="font-weight: 500; color: #374151;">Account:</span> ********</p>
          </div>
          <div style="line-height: 1.6;">
            <p style="margin: 8px 0;"><span style="font-weight: 500; color: #374151;">IBAN:</span> ***********************</p>
            <p style="margin: 8px 0;"><span style="font-weight: 500; color: #374151;">SWIFT:</span> UNBKILIT</p>
            <p style="margin: 8px 0;"><span style="font-weight: 500; color: #374151;">Beneficiary:</span> ${escapeHtml(invoiceData.fromCompany.name)}</p>
          </div>
        </div>
      </div>
      
      <!-- Notes -->
      ${invoiceData.notes ? `
        <div style="border-top: 1px solid #e5e7eb; padding-top: 24px;">
          <h4 style="font-weight: 600; color: #111827; margin: 0 0 8px 0;">Notes:</h4>
          <div style="color: #374151; font-size: 14px; white-space: pre-wrap; line-height: 1.6;">
            ${escapeHtml(invoiceData.notes)}
          </div>
        </div>
      ` : ''}
      
      <!-- Footer -->
      <div style="text-align: center; font-size: 12px; color: #6b7280; margin-top: 32px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
        <p style="margin: 0;">Thank you for your business!</p>
        <p style="margin: 4px 0 0 0;">This invoice was generated electronically and is valid without signature.</p>
      </div>
    </div>
  `;
  
  return element;
}

function generateFilename(invoiceData: InvoiceData): string {
  const invoiceNumber = invoiceData.invoiceNumber || "DRAFT";
  const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  return `Invoice_#${invoiceNumber}_${date}.pdf`;
}